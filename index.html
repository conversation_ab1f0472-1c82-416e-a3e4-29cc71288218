<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeecHive - Empowering Speech-Language Therapy Through Community</title>
    <meta name="description" content="Join SpeecHive's free community platform for speech-language therapy students and parents of children with hearing loss. Access mentorship, resources, and support - completely free, always.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://speechive.com/">
    <meta property="og:title" content="SpeecHive - Free Speech-Language Therapy Community">
    <meta property="og:description" content="Join our thriving community of speech-language therapy students and parents. Free mentorship, resources, and support for everyone.">
    <meta property="og:image" content="images/speechive-hero.jpg">
    <meta property="og:image:alt" content="SpeecHive Community Platform">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://speechive.com/">
    <meta name="twitter:title" content="SpeecHive - Free Speech-Language Therapy Community">
    <meta name="twitter:description" content="Join our thriving community of speech-language therapy students and parents. Free mentorship, resources, and support for everyone.">
    <meta name="twitter:image" content="images/speechive-hero.jpg">
    <meta name="twitter:image:alt" content="SpeecHive Community Platform">

    <!-- Critical CSS loaded directly for faster rendering -->
    <link rel="stylesheet" href="css/critical.css">

    <!-- Preload hero video for faster loading -->
    <link rel="preload" href="./30fps.mp4" as="video" type="video/mp4">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- External CSS resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap">


    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f59e0b', // Warm amber/honey color for bee theme
                        secondary: '#64748b', // Slate gray
                        accent: '#0ea5e9', // Sky blue for trust/healthcare
                        light: '#fefefe', // Pure white background
                        dark: '#1e293b', // Dark slate for text
                        honey: '#fbbf24', // Golden honey color
                        hive: '#f3f4f6' // Light gray for sections
                    },
                    fontFamily: {
                        sans: ['Poppins', 'sans-serif'],
                        heading: ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://speechive.com/#website",
                "url": "https://speechive.com/",
                "name": "SpeecHive - Speech-Language Therapy Community",
                "description": "Free community platform for speech-language therapy education and support",
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": "https://speechive.com/?s={search_term_string}",
                    "query-input": "required name=search_term_string"
                },
                "inLanguage": "en"
            },
            {
                "@type": "Organization",
                "@id": "https://speechive.com/#organization",
                "name": "SpeecHive",
                "url": "https://speechive.com/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://speechive.com/images/speechive-logo.png",
                    "width": 300,
                    "height": 100,
                    "caption": "SpeecHive - Speech-Language Therapy Community"
                },
                "sameAs": [
                    "https://chat.whatsapp.com/speechive-community"
                ],
                "email": "<EMAIL>",
                "description": "Revolutionary platform dedicated to breaking down barriers in speech-language therapy education and support through free community-driven learning.",
                "foundingDate": "2024",
                "areaServed": {
                    "@type": "Place",
                    "name": "Global"
                },
                "knowsAbout": [
                    "Speech-Language Pathology",
                    "Hearing Loss Support",
                    "Student Mentorship",
                    "Parent Education",
                    "Community Building"
                ]
            },
            {
                "@type": "EducationalOrganization",
                "@id": "https://speechive.com/#educational-organization",
                "name": "SpeecHive",
                "url": "https://speechive.com/",
                "description": "Free speech-language therapy education and support platform",
                "hasOfferCatalog": {
                    "@type": "OfferCatalog",
                    "name": "Educational Programs",
                    "itemListElement": [
                        {
                            "@type": "Course",
                            "name": "Student Development Track",
                            "description": "Comprehensive mentorship and support for speech-language therapy students",
                            "provider": {
                                "@type": "Organization",
                                "name": "SpeecHive"
                            },
                            "courseMode": "Online",
                            "educationalLevel": "Undergraduate to Professional",
                            "inLanguage": "en",
                            "isAccessibleForFree": true,
                            "audience": {
                                "@type": "Audience",
                                "audienceType": "Speech-Language Therapy Students"
                            }
                        },
                        {
                            "@type": "Course",
                            "name": "Parent Support Track",
                            "description": "Support and education for parents of children with hearing loss",
                            "provider": {
                                "@type": "Organization",
                                "name": "SpeecHive"
                            },
                            "courseMode": "Online",
                            "inLanguage": "en",
                            "isAccessibleForFree": true,
                            "audience": {
                                "@type": "Audience",
                                "audienceType": "Parents of Children with Hearing Loss"
                            }
                        }
                    ]
                }
            },
            {
                "@type": "WebPage",
                "@id": "https://speechive.com/#webpage",
                "url": "https://speechive.com/",
                "name": "Home - SpeecHive Community",
                "isPartOf": {
                    "@id": "https://speechive.com/#website"
                },
                "about": {
                    "@id": "https://speechive.com/#organization"
                },
                "description": "Join our free community platform for speech-language therapy students and parents. Access mentorship, resources, and support.",
                "inLanguage": "en",
                "potentialAction": [
                    {
                        "@type": "ReadAction",
                        "target": ["https://speechive.com/"]
                    }
                ]
            },
            {
                "@type": "BreadcrumbList",
                "@id": "https://speechive.com/#breadcrumb",
                "itemListElement": [
                    {
                        "@type": "ListItem",
                        "position": 1,
                        "item": {
                            "@id": "https://speechive.com/",
                            "name": "Home"
                        }
                    }
                ]
            }
        ]
    }
    </script>
</head>
<body class="bg-light font-sans text-dark">
    <!-- Loading Screen -->
    <div class="loader-container" id="loader">
        <div class="book-animation">
            <i class="fas fa-users"></i>
        </div>
        <p class="text-primary font-bold text-3xl"><i class="fas fa-users mr-2"></i>Welcome to the Hive</p>
        <div class="loader-text">Building our community together...</div>
        <div class="loader-progress">
            <div class="progress-bar" id="progress"></div>
        </div>
    </div>

    <div id="main-content">
    <!-- Header with Navigation -->
    <header class="bg-white/95 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300 backdrop-blur-sm">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <img src="images/speechive-logo.png" alt="SpeecHive - Speech-Language Therapy Community" class="h-12">
                        <span class="text-2xl font-bold text-primary">SpeecHive</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-primary font-medium hover:text-dark transition">Home</a>
                    <a href="/about/" class="text-dark font-medium hover:text-primary transition">About Us</a>
                    <a href="/news/" class="text-dark font-medium hover:text-primary transition">Community</a>
                    <a href="/events/" class="text-dark font-medium hover:text-primary transition">Success Stories</a>
                    <a href="/resources/" class="text-dark font-medium hover:text-primary transition">Resources</a>
                    <a href="/contact_form/" class="text-dark font-medium hover:text-primary transition">Contact</a>
                    <a href="/registration_form/" class="bg-primary hover:bg-honey text-white px-6 py-2 rounded-lg font-medium transition flex items-center justify-center">Join Our Hive</a>
                </div>

                <!-- Mobile Navigation Toggle -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-dark hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </nav>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden fixed inset-0 bg-white bg-opacity-95 z-50 backdrop-blur-sm">
                <!-- Close button -->
                <button id="mobile-menu-close" class="absolute top-6 right-6 text-dark hover:text-primary">
                    <i class="fas fa-times text-2xl"></i>
                </button>

                <!-- Menu content -->
                <div class="flex flex-col items-center justify-center h-full w-full">
                    <div class="flex flex-col items-center space-y-10 py-8">
                        <a href="/" class="text-primary text-2xl font-medium hover:text-dark transition">Home</a>
                        <a href="/about/" class="text-dark text-2xl font-medium hover:text-primary transition">About Us</a>
                        <a href="/news/" class="text-dark text-2xl font-medium hover:text-primary transition">Community</a>
                        <a href="/events/" class="text-dark text-2xl font-medium hover:text-primary transition">Success Stories</a>
                        <a href="/resources/" class="text-dark text-2xl font-medium hover:text-primary transition">Resources</a>
                        <a href="/contact_form/" class="text-dark text-2xl font-medium hover:text-primary transition">Contact</a>
                        <a href="/registration_form/" class="text-dark text-2xl font-medium hover:text-primary transition">Join Our Hive</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative overflow-hidden hero-section bg-gradient-to-br from-light to-hive pt-16" id="hero">
        <div class="absolute inset-0 w-full h-full">
            <!-- Honeycomb pattern background -->
            <div class="absolute inset-0 opacity-5">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="honeycomb" x="0" y="0" width="56" height="100" patternUnits="userSpaceOnUse">
                            <polygon points="28,0 56,16 56,48 28,64 0,48 0,16" fill="none" stroke="#f59e0b" stroke-width="1"/>
                            <polygon points="28,64 56,80 56,112 28,128 0,112 0,80" fill="none" stroke="#f59e0b" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#honeycomb)"/>
                </svg>
            </div>
        </div>

        <!-- Content Container -->
        <div class="relative h-full flex items-center justify-center min-h-screen">
            <div class="container mx-auto px-4">
                <div class="flex flex-col lg:flex-row items-center justify-between max-w-6xl mx-auto">
                    <!-- Left side - Content -->
                    <div class="lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0">
                        <div class="flex items-center justify-center lg:justify-start mb-6">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-users text-white text-2xl"></i>
                            </div>
                            <h1 class="text-5xl md:text-6xl font-bold text-dark">
                                SpeecHive
                            </h1>
                        </div>

                        <h2 class="text-2xl md:text-3xl mb-6 font-semibold text-secondary">
                            Empowering Speech-Language Therapy<br>Through Community
                        </h2>

                        <p class="text-lg mb-8 text-dark max-w-xl mx-auto lg:mx-0 leading-relaxed">
                            Join our thriving, free community where speech-language therapy students and parents of children with hearing loss connect, learn, and grow together. No barriers, no costs - just pure support.
                        </p>

                        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                            <a href="/registration_form/" class="bg-primary hover:bg-honey text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 text-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                <i class="fas fa-rocket mr-2"></i>Join Our Hive - It's Free!
                            </a>
                            <a href="/about/" class="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 text-center">
                                Learn More
                            </a>
                        </div>

                        <div class="mt-8 flex items-center justify-center lg:justify-start space-x-6 text-sm text-secondary">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                100% Free Forever
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-users text-accent mr-2"></i>
                                Growing Community
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-heart text-red-500 mr-2"></i>
                                Expert Support
                            </div>
                        </div>
                    </div>

                    <!-- Right side - Visual -->
                    <div class="lg:w-1/2 flex justify-center">
                        <div class="relative">
                            <div class="w-80 h-80 bg-gradient-to-br from-primary to-honey rounded-full flex items-center justify-center shadow-2xl">
                                <div class="text-center text-white">
                                    <div class="text-6xl mb-4">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <h3 class="text-2xl font-bold mb-2">Welcome Home</h3>
                                    <p class="text-lg opacity-90">Your community awaits</p>
                                </div>
                            </div>
                            <!-- Floating elements -->
                            <div class="absolute -top-4 -right-4 w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white text-2xl animate-bounce">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-2xl animate-bounce" style="animation-delay: 0.5s;">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="absolute top-1/2 -left-8 w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white text-xl animate-bounce" style="animation-delay: 1s;">
                                <i class="fas fa-comments"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Who We Serve Section -->
    <section id="who-we-serve" class="relative overflow-hidden py-24 bg-white">
        <!-- Content Container -->
        <div class="relative container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <!-- Section Header -->
                <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-5xl font-bold text-dark mb-4">Who We Serve</h2>
                    <div class="w-32 h-2 bg-primary mx-auto mb-6"></div>
                    <p class="text-xl text-secondary max-w-3xl mx-auto">
                        Our hive welcomes everyone in the speech-language therapy community. Whether you're just starting your journey or supporting a loved one, there's a place for you here.
                    </p>
                </div>

                <!-- Two Main Groups -->
                <div class="grid md:grid-cols-2 gap-12 mb-16">
                    <!-- Students Section -->
                    <div class="bg-gradient-to-br from-accent/10 to-accent/5 p-8 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300" data-aos="fade-right" data-aos-duration="500">
                        <div class="text-center mb-6">
                            <div class="w-20 h-20 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-white text-3xl"></i>
                            </div>
                            <h3 class="text-3xl font-bold text-dark mb-4">Speech-Language Therapy Students</h3>
                            <p class="text-secondary text-lg leading-relaxed">
                                Whether you're an undergraduate just beginning your journey, a graduate student navigating clinical requirements, or a clinical fellow seeking additional mentorship, SpeecHive provides the support you need.
                            </p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-check-circle text-accent mt-1"></i>
                                <span class="text-dark">Peer mentorship from students who've walked your path</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-check-circle text-accent mt-1"></i>
                                <span class="text-dark">Professional guidance from experienced clinicians</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-check-circle text-accent mt-1"></i>
                                <span class="text-dark">Study groups and collaborative learning opportunities</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-check-circle text-accent mt-1"></i>
                                <span class="text-dark">Career development and job search support</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-check-circle text-accent mt-1"></i>
                                <span class="text-dark">Real-world clinical insights beyond textbook learning</span>
                            </div>
                        </div>
                    </div>

                    <!-- Parents Section -->
                    <div class="bg-gradient-to-br from-green-100 to-green-50 p-8 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300" data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                        <div class="text-center mb-6">
                            <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-users text-white text-3xl"></i>
                            </div>
                            <h3 class="text-3xl font-bold text-dark mb-4">Parents of Children with Hearing Loss</h3>
                            <p class="text-secondary text-lg leading-relaxed">
                                Navigating the world of hearing loss with your child can feel overwhelming. SpeecHive recognizes that parents are their child's first and most important teachers.
                            </p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-red-500 mt-1"></i>
                                <span class="text-dark">Learn practical communication strategies</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-red-500 mt-1"></i>
                                <span class="text-dark">Connect with other families on similar journeys</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-red-500 mt-1"></i>
                                <span class="text-dark">Access expert guidance on educational advocacy</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-red-500 mt-1"></i>
                                <span class="text-dark">Understand technology and equipment options</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-red-500 mt-1"></i>
                                <span class="text-dark">Build confidence in supporting your child's development</span>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-red-500 mt-1"></i>
                                <span class="text-dark">Find emotional support and encouragement</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="text-center" data-aos="fade-up" data-aos-duration="500" data-aos-delay="400">
                    <div class="bg-gradient-to-r from-primary to-honey p-8 rounded-2xl text-white shadow-xl">
                        <h3 class="text-3xl font-bold mb-4">Ready to Join Our Community?</h3>
                        <p class="text-xl mb-6 opacity-90">
                            It takes less than 2 minutes to enroll, and you'll immediately gain access to our supportive WhatsApp community, weekly coaching sessions, and extensive resource library.
                        </p>
                        <a href="/registration_form/" class="bg-white text-primary hover:bg-gray-100 px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 inline-block shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-rocket mr-2"></i>Start Your Journey Today
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- What Makes Us Different Section -->
    <section id="what-makes-us-different" class="py-20 bg-hive relative overflow-hidden">
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-6xl mx-auto">
                <!-- Section Header -->
                <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-5xl font-bold text-dark mb-4">What Makes Us Different</h2>
                    <div class="w-32 h-2 bg-primary mx-auto mb-6"></div>
                    <p class="text-xl text-secondary max-w-3xl mx-auto">
                        We're not just another platform - we're a movement built on revolutionary principles that put community and accessibility first.
                    </p>
                </div>

                <!-- Three Key Differentiators -->
                <div class="grid md:grid-cols-3 gap-8 mb-16">
                    <!-- 100% Free Always -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-duration="500" data-aos-delay="100">
                        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-gift text-green-600 text-3xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4">100% Free, Always</h3>
                        <p class="text-secondary leading-relaxed mb-6">
                            We firmly believe that financial barriers should never prevent access to quality education and support. Every resource, session, and service we offer is completely free. No hidden costs, no premium tiers, no subscription fees.
                        </p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-green-700 font-semibold">This isn't just a business model – it's a core value that drives everything we do.</p>
                        </div>
                    </div>

                    <!-- Train to Train Philosophy -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-duration="500" data-aos-delay="300">
                        <div class="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-recycle text-primary text-3xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4">Train to Train Philosophy</h3>
                        <p class="text-secondary leading-relaxed mb-6">
                            Our unique approach creates a sustainable, ever-growing community. Members start by learning and receiving support, then gradually become mentors and teachers themselves.
                        </p>
                        <div class="space-y-2 text-left">
                            <div class="flex items-center">
                                <i class="fas fa-arrow-right text-primary mr-2"></i>
                                <span class="text-sm text-secondary">Knowledge is continuously shared</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-arrow-right text-primary mr-2"></i>
                                <span class="text-sm text-secondary">Everyone contributes their expertise</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-arrow-right text-primary mr-2"></i>
                                <span class="text-sm text-secondary">Community grows stronger together</span>
                            </div>
                        </div>
                    </div>

                    <!-- Real-World Focus -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-duration="500" data-aos-delay="500">
                        <div class="w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-hands-helping text-accent text-3xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4">Real-World Focus</h3>
                        <p class="text-secondary leading-relaxed mb-6">
                            While academic knowledge is important, we emphasize practical, real-world applications. Our programs bridge the gap between classroom theory and clinical practice.
                        </p>
                        <div class="bg-accent/10 p-4 rounded-lg">
                            <p class="text-accent font-semibold">Providing insights that textbooks simply can't offer.</p>
                        </div>
                    </div>
                </div>

                <!-- The Hive Philosophy -->
                <div class="bg-gradient-to-r from-primary to-honey p-8 rounded-2xl text-white shadow-xl mb-16" data-aos="fade-up" data-aos-duration="500">
                    <div class="text-center mb-8">
                        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-white text-3xl"></i>
                        </div>
                        <h3 class="text-3xl font-bold mb-4">The Hive Philosophy</h3>
                        <p class="text-xl opacity-90 max-w-3xl mx-auto">
                            Just like a beehive, our community thrives on collaboration, shared purpose, and collective growth. Each member plays a vital role in creating something greater than the sum of its parts.
                        </p>
                    </div>

                    <div class="grid md:grid-cols-5 gap-6 text-center">
                        <div class="transform hover:scale-105 transition-all duration-300">
                            <div class="text-3xl mb-2">
                                <i class="fas fa-handshake text-white"></i>
                            </div>
                            <h4 class="font-bold mb-2">Community Collaboration</h4>
                            <p class="text-sm opacity-80">Working together toward common goals</p>
                        </div>
                        <div class="transform hover:scale-105 transition-all duration-300">
                            <div class="text-3xl mb-2">
                                <i class="fas fa-book text-white"></i>
                            </div>
                            <h4 class="font-bold mb-2">Structured Learning</h4>
                            <p class="text-sm opacity-80">Organized, systematic approach to education</p>
                        </div>
                        <div class="transform hover:scale-105 transition-all duration-300">
                            <div class="text-3xl mb-2">
                                <i class="fas fa-heart text-white"></i>
                            </div>
                            <h4 class="font-bold mb-2">Sweet Rewards</h4>
                            <p class="text-sm opacity-80">The joy of helping others communicate</p>
                        </div>
                        <div class="transform hover:scale-105 transition-all duration-300">
                            <div class="text-3xl mb-2">
                                <i class="fas fa-dumbbell text-white"></i>
                            </div>
                            <h4 class="font-bold mb-2">Industrious Spirit</h4>
                            <p class="text-sm opacity-80">Dedicated approach to development</p>
                        </div>
                        <div class="transform hover:scale-105 transition-all duration-300">
                            <div class="text-3xl mb-2">
                                <i class="fas fa-link text-white"></i>
                            </div>
                            <h4 class="font-bold mb-2">Interconnected Support</h4>
                            <p class="text-sm opacity-80">Every member strengthens the whole</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How We Work Section -->
    <section id="how-we-work" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <!-- Section Header -->
                <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-5xl font-bold text-dark mb-4">How We Work</h2>
                    <div class="w-32 h-2 bg-primary mx-auto mb-6"></div>
                    <p class="text-xl text-secondary max-w-3xl mx-auto">
                        Our approach combines multiple touchpoints to create a comprehensive support system that works around your schedule and learning style.
                    </p>
                </div>

                <!-- Four Ways We Work -->
                <div class="grid md:grid-cols-2 gap-8 mb-16">
                    <!-- Weekly Coaching Sessions -->
                    <div class="bg-gradient-to-br from-accent/10 to-accent/5 p-8 rounded-2xl shadow-lg" data-aos="fade-right" data-aos-duration="500">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-accent rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-video text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-dark mb-4">Weekly Coaching Sessions</h3>
                                <p class="text-secondary leading-relaxed mb-4">
                                    Every week, we host live, interactive sessions led by experienced professionals. These aren't lectures – they're collaborative discussions where you can ask questions, share experiences, and learn from peers.
                                </p>
                                <div class="bg-accent/10 p-3 rounded-lg">
                                    <p class="text-accent font-semibold text-sm"><i class="fas fa-video mr-2"></i>Sessions are recorded for those who can't attend live</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- WhatsApp Community -->
                    <div class="bg-gradient-to-br from-green-100 to-green-50 p-8 rounded-2xl shadow-lg" data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fab fa-whatsapp text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-dark mb-4">WhatsApp Community</h3>
                                <p class="text-secondary leading-relaxed mb-4">
                                    Our vibrant WhatsApp community serves as the heart of our platform. It's where daily conversations happen, questions are answered quickly, resources are shared, and friendships are formed.
                                </p>
                                <div class="bg-green-100 p-3 rounded-lg">
                                    <p class="text-green-700 font-semibold text-sm"><i class="fas fa-comments mr-2"></i>Active 24/7, providing support whenever you need it</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resource Library -->
                    <div class="bg-gradient-to-br from-primary/10 to-honey/10 p-8 rounded-2xl shadow-lg" data-aos="fade-right" data-aos-duration="500" data-aos-delay="400">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-book-open text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-dark mb-4">Resource Library</h3>
                                <p class="text-secondary leading-relaxed mb-4">
                                    We maintain an extensive library of downloadable resources, including guides, templates, checklists, and educational materials. All resources are professionally reviewed and completely free to access.
                                </p>
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <p class="text-primary font-semibold text-sm"><i class="fas fa-book mr-2"></i>Continuously updated with new materials</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mentorship Matching -->
                    <div class="bg-gradient-to-br from-red-100 to-red-50 p-8 rounded-2xl shadow-lg" data-aos="fade-left" data-aos-duration="500" data-aos-delay="600">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-handshake text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-dark mb-4">Mentorship Matching</h3>
                                <p class="text-secondary leading-relaxed mb-4">
                                    We connect students with experienced mentors and help parents find other families with similar experiences. These relationships often become lasting friendships that extend far beyond our platform.
                                </p>
                                <div class="bg-red-100 p-3 rounded-lg">
                                    <p class="text-red-700 font-semibold text-sm"><i class="fas fa-heart mr-2"></i>Building lifelong connections and support networks</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="text-center" data-aos="fade-up" data-aos-duration="500" data-aos-delay="800">
                    <div class="bg-gradient-to-r from-dark to-secondary p-8 rounded-2xl text-white shadow-xl">
                        <h3 class="text-3xl font-bold mb-4">Ready to Experience the Difference?</h3>
                        <p class="text-xl mb-6 opacity-90 max-w-2xl mx-auto">
                            Join thousands of students and parents who have found their home in our supportive community. Your journey starts with a single click.
                        </p>
                        <a href="/registration_form/" class="bg-primary hover:bg-honey text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 inline-block shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-rocket mr-2"></i>Join Our Hive Today
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-primary/5"></div>
    </section>

    <!-- Our Programs Section -->
    <section id="programs" class="py-20 bg-dark text-white relative overflow-hidden">
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-5xl font-bold text-primary mb-4">Our Programs</h2>
                    <div class="w-32 h-2 bg-primary mx-auto mb-6"></div>
                    <p class="text-white/80 text-xl max-w-3xl mx-auto">
                        We offer two comprehensive tracks designed to meet the unique needs of our community members. Each program is carefully crafted to provide maximum value and support.
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-12">
                    <!-- Student Development Track -->
                    <div class="bg-gradient-to-br from-accent/20 to-accent/10 p-8 rounded-2xl border border-accent/30 hover:border-accent/50 transition-all duration-300" data-aos="fade-right" data-aos-duration="500">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-accent rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-graduation-cap text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-white mb-2">Student Development Track</h3>
                                <p class="text-accent font-semibold">For Speech-Language Therapy Students</p>
                            </div>
                        </div>

                        <p class="text-white/80 mb-6 leading-relaxed">
                            Designed specifically for speech-language therapy students at all levels, this comprehensive program covers everything you need to succeed in your academic and clinical journey.
                        </p>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-stethoscope text-accent mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Clinical Skills Development</h4>
                                    <p class="text-white/70 text-sm">Master essential assessment and treatment techniques through hands-on guidance</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-briefcase text-accent mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Professional Development</h4>
                                    <p class="text-white/70 text-sm">Build your professional identity with resume building and career planning</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-microscope text-accent mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Research & Evidence-Based Practice</h4>
                                    <p class="text-white/70 text-sm">Learn to integrate current research into clinical decision-making</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-user-friends text-accent mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Peer Mentorship</h4>
                                    <p class="text-white/70 text-sm">Connect with experienced students and recent graduates</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Parent Support Track -->
                    <div class="bg-gradient-to-br from-green-500/20 to-green-400/10 p-8 rounded-2xl border border-green-400/30 hover:border-green-400/50 transition-all duration-300" data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-users text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-white mb-2">Parent Support Track</h3>
                                <p class="text-green-400 font-semibold">For Parents of Children with Hearing Loss</p>
                            </div>
                        </div>

                        <p class="text-white/80 mb-6 leading-relaxed">
                            Tailored for parents of children with hearing loss, offering practical tools, emotional support, and expert guidance to help you navigate this journey with confidence.
                        </p>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-comments text-green-400 mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Communication Strategies</h4>
                                    <p class="text-white/70 text-sm">Learn effective techniques for supporting your child's language development</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-cogs text-green-400 mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Technology & Equipment</h4>
                                    <p class="text-white/70 text-sm">Understand hearing aids, cochlear implants, and assistive technology</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-school text-green-400 mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Educational Advocacy</h4>
                                    <p class="text-white/70 text-sm">Navigate the school system and understand IEPs and 504 plans</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-heart text-green-400 mt-1"></i>
                                <div>
                                    <h4 class="text-white font-semibold">Emotional Support & Wellness</h4>
                                    <p class="text-white/70 text-sm">Address emotional aspects and build resilience through self-care</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="text-center mt-16" data-aos="fade-up" data-aos-duration="500" data-aos-delay="400">
                    <h3 class="text-3xl font-bold text-white mb-4">Ready to Choose Your Path?</h3>
                    <p class="text-white/80 text-lg mb-8 max-w-2xl mx-auto">
                        Both tracks are completely free and designed to grow with you. Join the program that fits your needs and start your journey today.
                    </p>
                    <a href="/registration_form/" class="bg-primary hover:bg-honey text-dark px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 inline-block shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-sign-in-alt mr-2"></i>Choose Your Track
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section id="success-stories" class="py-24 bg-white relative overflow-hidden">
        <div class="container mx-auto px-4 relative">
            <!-- Section header -->
            <div class="text-center mb-16 max-w-3xl mx-auto" data-aos="fade-up" data-aos-duration="500">
                <h2 class="text-5xl font-bold text-dark mb-4">Success Stories</h2>
                <div class="w-32 h-2 bg-primary mx-auto mb-6"></div>
                <p class="text-xl text-secondary">
                    Real stories from our community members who have found support, growth, and success through SpeecHive
                </p>
            </div>

            <!-- Success Stories Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">

                <!-- Success Story 1 - Student -->
                <div class="bg-gradient-to-br from-accent/10 to-accent/5 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-accent rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-graduation-cap text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-dark">Sarah M.</h3>
                            <p class="text-secondary text-sm">Graduate Student, SLP</p>
                        </div>
                    </div>
                    <p class="text-dark leading-relaxed mb-4">
                        "SpeecHive transformed my clinical experience. The mentorship I received helped me navigate my CFY with confidence. The community support was invaluable during challenging cases."
                    </p>
                    <div class="flex items-center text-primary">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <span class="ml-2 text-secondary text-sm">5/5 stars</span>
                    </div>
                </div>

                <!-- Success Story 2 - Parent -->
                <div class="bg-gradient-to-br from-green-100 to-green-50 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-dark">Maria L.</h3>
                            <p class="text-secondary text-sm">Parent of child with hearing loss</p>
                        </div>
                    </div>
                    <p class="text-dark leading-relaxed mb-4">
                        "I felt so lost when my daughter was diagnosed. SpeecHive connected me with other parents who understood. The practical strategies I learned have made such a difference in our daily communication."
                    </p>
                    <div class="flex items-center text-primary">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <span class="ml-2 text-secondary text-sm">5/5 stars</span>
                    </div>
                </div>

                <!-- Success Story 3 - Student -->
                <div class="bg-gradient-to-br from-primary/10 to-honey/10 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user-graduate text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-dark">James K.</h3>
                            <p class="text-secondary text-sm">Undergraduate Student</p>
                        </div>
                    </div>
                    <p class="text-dark leading-relaxed mb-4">
                        "The study groups and peer mentorship helped me excel in my coursework. I went from struggling with phonetics to helping other students. The community truly lifts each other up."
                    </p>
                    <div class="flex items-center text-primary">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <span class="ml-2 text-secondary text-sm">5/5 stars</span>
                    </div>
                </div>

                <!-- Success Story 4 - Parent -->
                <div class="bg-gradient-to-br from-red-100 to-red-50 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="400">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-heart text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-dark">David R.</h3>
                            <p class="text-secondary text-sm">Father of twins with hearing loss</p>
                        </div>
                    </div>
                    <p class="text-dark leading-relaxed mb-4">
                        "Having twins with hearing loss felt overwhelming. The parent support track gave us practical tools and emotional support. We're now confident advocates for our children's needs."
                    </p>
                    <div class="flex items-center text-primary">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <span class="ml-2 text-secondary text-sm">5/5 stars</span>
                    </div>
                </div>

                <!-- Success Story 5 - Student -->
                <div class="bg-gradient-to-br from-purple-100 to-purple-50 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="500">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-lightbulb text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-dark">Emily T.</h3>
                            <p class="text-secondary text-sm">Clinical Fellow</p>
                        </div>
                    </div>
                    <p class="text-dark leading-relaxed mb-4">
                        "The real-world focus of SpeecHive prepared me for challenges textbooks never covered. My mentor helped me develop clinical reasoning skills that made me a better clinician."
                    </p>
                    <div class="flex items-center text-primary">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <span class="ml-2 text-secondary text-sm">5/5 stars</span>
                    </div>
                </div>

                <!-- Success Story 6 - Parent -->
                <div class="bg-gradient-to-br from-blue-100 to-blue-50 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="600">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-comments text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-dark">Lisa H.</h3>
                            <p class="text-secondary text-sm">Mother of teenager with hearing loss</p>
                        </div>
                    </div>
                    <p class="text-dark leading-relaxed mb-4">
                        "The WhatsApp community became my lifeline. Having 24/7 access to other parents who truly understand made all the difference during difficult moments. We're not alone anymore."
                    </p>
                    <div class="flex items-center text-primary">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <span class="ml-2 text-secondary text-sm">5/5 stars</span>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center" data-aos="fade-up" data-aos-duration="500" data-aos-delay="700">
                <div class="bg-gradient-to-r from-primary to-honey p-8 rounded-2xl text-white shadow-xl max-w-4xl mx-auto">
                    <h3 class="text-3xl font-bold mb-4">Ready to Write Your Success Story?</h3>
                    <p class="text-xl mb-6 opacity-90">
                        Join thousands of students and parents who have found their community, support, and success through SpeecHive. Your journey starts today.
                    </p>
                    <a href="/registration_form/" class="bg-white text-primary hover:bg-gray-100 px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 inline-block shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-pen-alt mr-2"></i>Start Your Story
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Counter Section -->
    <section id="impact" class="py-20 relative overflow-hidden">
        <!-- Background image -->
        <div class="absolute inset-0 w-full h-full">
            <img src="images/kaaba2.webp" alt="Kaaba in Mecca with pilgrims - background for impact section" class="absolute inset-0 min-w-full min-h-full object-cover" >
            <!-- Dark overlay - reduced opacity to make Kaaba more visible -->
            <div class="absolute inset-0 bg-black opacity-60"></div>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                <h2 class="text-4xl font-bold text-white mb-4">Our Impact</h2>
                <p class="text-xl text-white/80">Khuddam Ul Quran Proudly Serving Muslims Around Aotearoa, New Zealand and Beyond</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-4xl mx-auto">
                <!-- Counter 1 -->
                <div class="text-center">
                    <div class="text-6xl md:text-7xl font-bold text-primary mb-4 counter-value" data-count="121">0</div>
                    <p class="text-white text-xl">Active Students</p>
                </div>

                <!-- Counter 2 -->
                <div class="text-center">
                    <div class="text-6xl md:text-7xl font-bold text-primary mb-4 counter-value" data-count="276">0</div>
                    <p class="text-white text-xl">Graduates</p>
                </div>

                <!-- Counter 3 -->
                <div class="text-center">
                    <div class="text-6xl md:text-7xl font-bold text-primary mb-4 counter-value" data-count="5">0</div>
                    <p class="text-white text-xl">Countries Reached</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-gray-50 text-gray-800 relative overflow-hidden">
        <img src="./images/quran-photo.webp" alt="Quran Background" class="absolute inset-0 w-full h-full object-cover opacity-20">

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-8 sm:mb-12" data-aos="fade-up" data-aos-duration="500">
                <h2 class="text-3xl sm:text-4xl font-bold mb-3 sm:mb-4 text-primary-black">Impact Stories</h2>
                <div class="w-16 sm:w-24 h-1 bg-primary mx-auto mb-4 sm:mb-8"></div>
                <p class="max-w-3xl mx-auto text-base sm:text-lg md:text-xl text-gray-900 px-4">
                    Hear from our students about how Khuddam's Arabic Grammar program has transformed their understanding of the Quran
                </p>
            </div>

            <!-- Crossfade with Quote Icon Testimonial Carousel -->
            <div class="relative max-w-3xl mx-auto" id="testimonial-container" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
                <!-- Fixed Quote Icon -->
                <div class="absolute top-10 left-1/2 transform -translate-x-1/2 z-20">
                    <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
                        <i class="fas fa-quote-right text-primary text-3xl"></i>
                    </div>
                </div>

                <!-- Testimonials Wrapper -->
                <div class="bg-white/40 backdrop-blur-[10px] p-6 sm:p-8 border-2 border-primary shadow-lg rounded-sm min-h-[350px] relative overflow-hidden">
                    <!-- Testimonial 1 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "Before joining Khuddam's Arabic Grammar program, I struggled to understand the deeper meanings of the Quran. The structured approach to learning grammar has transformed my ability to connect with Allah's words."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Ahmed Ibrahim</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Graduate</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "The Arabic Grammar course at Khuddam has completely changed how I read the Quran. Understanding the difference between Ism, Fi'l, and Harf has given me insights I never thought possible."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Waqar Raja</h4>
                                <p class="text-gray-500 text-sm">Intermediate Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "As someone who tried to learn Arabic grammar multiple times before, Khuddam's approach is truly unique. The focus on Quranic application rather than abstract rules makes all the difference."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Zaka Chohan</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 4 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "Learning Arabic grammar with Khuddam has been a transformative journey. The way they connect grammar rules directly to Quranic verses makes the learning process meaningful and spiritually enriching."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Saad Mahmood</h4>
                                <p class="text-gray-500 text-sm">Advanced Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 5 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "I've tried many Arabic courses before, but Khuddam's approach to grammar is exceptional. They focus on practical application rather than overwhelming theory. Now I can actually understand the grammatical structure of Quranic verses."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Farhan Qureshi</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Graduate</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 6 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "The step-by-step approach to Arabic grammar at Khuddam has been perfect for me. Each lesson builds on the previous one in a logical way, making complex concepts accessible."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Imran Siddiqui</h4>
                                <p class="text-gray-500 text-sm">Intermediate Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 7 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "What sets Khuddam's Arabic grammar program apart is how they connect every rule to practical examples from the Quran. This approach has not only improved my understanding of Arabic but has deepened my connection with the Quran itself."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Yasir Malik</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Graduate</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 8 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "The Arabic grammar classes have given me a new perspective on the Quran. I can now appreciate the linguistic beauty and precision that I was missing before. It's like seeing the Quran in high definition."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Umar Farooq</h4>
                                <p class="text-gray-500 text-sm">Advanced Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 9 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "I was hesitant to start learning Arabic grammar at my age, but Khuddam's program made it accessible and enjoyable. The community of learners and the supportive teachers have made this journey a blessing in my life."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Abdul Rahman</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- No progress bar -->
                </div>

                <!-- Navigation Arrows -->
                <button class="absolute top-1/2 left-0 -translate-y-1/2 -translate-x-1 sm:-translate-x-4 md:-translate-x-8 bg-primary text-black w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center focus:outline-none shadow-lg z-10 hover:bg-primary/90 transition-all duration-300" id="prev-testimonial">
                    <i class="fas fa-chevron-left text-base sm:text-lg"></i>
                </button>
                <button class="absolute top-1/2 right-0 -translate-y-1/2 translate-x-1 sm:translate-x-4 md:translate-x-8 bg-primary text-black w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center focus:outline-none shadow-lg z-10 hover:bg-primary/90 transition-all duration-300" id="next-testimonial">
                    <i class="fas fa-chevron-right text-base sm:text-lg"></i>
                </button>

                <!-- Dots Indicator -->
                <div class="flex justify-center mt-8 sm:mt-10 space-x-2 sm:space-x-3">
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-primary rounded-full shadow-sm transition-all duration-300" data-index="0"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="1"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="2"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="3"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="4"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="5"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="6"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="7"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="8"></button>
                </div>
            </div>
        </div>
    </section>

    <!-- Community & Events CTA Section -->
    <section id="community-events-cta" class="py-20 bg-dark relative overflow-hidden">
        <img src="./images/image1.webp" alt="Quran Background" class="absolute inset-0 w-full h-full object-cover opacity-50">
            <div class="container mx-auto px-4 relative z-10">
                <div class="max-w-6xl mx-auto">
                    <!-- Section Header -->
                    <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                        <h2 class="text-4xl font-bold text-white mb-4">Explore Our Community</h2>
                        <div class="w-24 h-1 bg-primary mx-auto mb-6"></div>
                        <p class="text-lg text-white/80 max-w-3xl mx-auto">
                            Stay connected with our vibrant community and discover upcoming events that strengthen our bonds in faith and learning.
                        </p>
                    </div>

                    <!-- CTA Cards Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                        <!-- Community CTA Card -->
                        <div class="bg-white border-2 border-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-aos="fade-right" data-aos-duration="500" data-aos-delay="100">
                            <div class="p-8 text-center">
                                <!-- Icon -->
                                <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-users text-primary text-3xl"></i>
                                </div>

                                <!-- Content -->
                                <h3 class="text-2xl font-bold text-gray-800 mb-4">News Posts</h3>
                                <p class="text-gray-600 mb-6 leading-relaxed">
                                    Recent updates on our Arabic classes,  and community announcements that keep our ummah connected.
                                </p>

                                <!-- Features List -->
                                <div class="space-y-2 mb-8">
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Latest Class Updates</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Dawah Activities</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Community Announcements</span>
                                    </div>
                                </div>

                                <!-- CTA Button -->
                                <a href="/news/" class="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-black font-medium px-8 py-3 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                                    <span>View News</span>
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Events CTA Card -->
                        <div class="bg-white border-2 border-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                            <div class="p-8 text-center">
                                <!-- Icon -->
                                <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-calendar-alt text-primary text-3xl"></i>
                                </div>

                                <!-- Content -->
                                <h3 class="text-2xl font-bold text-gray-800 mb-4">Event Gallery</h3>
                                <p class="text-gray-600 mb-6 leading-relaxed">
                                    Browse through beautiful moments captured from our Arabic classes, community gatherings, and special Islamic events.
                                </p>

                                <!-- Features List -->
                                <div class="space-y-2 mb-8">
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Class Photo Galleries</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Event Highlights</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Community Memories</span>
                                    </div>
                                </div>

                                <!-- CTA Button -->
                                <a href="/events/" class="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-black font-medium px-8 py-3 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                                    <span>View Events</span>
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary text-dark -mt-1">
        <div class="container mx-auto px-4">
            <div class="max-w-5xl mx-auto flex flex-col md:flex-row items-center justify-between">
                <div class="mb-8 md:mb-0 text-center md:text-left" data-aos="fade-right" data-aos-duration="500">
                    <h2 class="text-3xl md:text-4xl font-bold mb-4">Have Questions?</h2>
                    <p class="text-lg md:text-xl text-dark/80 max-w-2xl">
                        Reach out to us with any questions about our Arabic Grammar program or other Islamic education opportunities.
                    </p>
                </div>
                <div data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                    <a href="/contact_form/" class="inline-block bg-dark hover:bg-dark/90 text-white font-medium px-8 py-4 transition-all duration-300">
                        <span class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Us
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="footer" class="bg-dark/95 shadow-md text-white -mt-1">
        <div class="container mx-auto px-4 py-10">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Logos -->
                <div class="mb-6 md:mb-0 flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="mr-2">
                            <img src="./images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12" >
                        </div>
                    </div>
                    <div class="flex items-center">
                        <a href="/">
                            <img src="./images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12" >
                        </a>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
                    <a href="/" class="text-primary font-medium hover:text-white transition">Home</a>
                    <a href="/about/" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="/news/" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="/events/" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="/resources/" class="text-white font-medium hover:text-primary transition">Resources</a>
                    <a href="/registration_form/" class="text-white font-medium hover:text-primary transition">Register Now</a>
                    <a href="/contact_form/" class="text-white font-medium hover:text-primary transition">Contact</a>
                </div>

                <!-- Social Media -->
                <div class="flex space-x-4">
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://www.youtube.com/@KhuddamUlQuranNZ" target="_blank" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-white/10 mt-8 pt-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
                    <div class="mt-4 md:mt-0">
                        <ul class="flex space-x-6 text-sm">
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Developer Credits -->
                <div class="mt-4 pt-4 border-t border-white/5">
                    <div class="text-center">
                        <p class="text-white/50 text-xs">
                            Website developed by
                            <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                                Kiwiorbit
                            </a>
                            | Contact:
                            <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                                +64 22 325 9094
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js" defer></script>
    </div><!-- End of main-content -->

    <!-- Loader Script -->
    <script src="js/loader.js" defer></script>

    <!-- Counter Animation Script -->
    <script>
        // Function to animate counter
        function animateCounter(element, duration) {
            const target = parseInt(element.getAttribute('data-count'));
            const start = 0;
            const increment = Math.ceil(target / (duration / 16)); // 16ms is roughly one frame at 60fps
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                } else {
                    element.textContent = current;
                }
            }, 16);
        }

        // Initialize counter animations
        document.addEventListener('DOMContentLoaded', function() {
            let countersAnimated = false;
            const counters = document.querySelectorAll('.counter-value');

            // Function to start counter animations
            function startCounterAnimations() {
                if (!countersAnimated) {
                    countersAnimated = true;
                    counters.forEach(counter => {
                        counter.classList.add('animated');
                        animateCounter(counter, 7000); // 7 seconds duration
                    });
                }
            }

            // Use Intersection Observer to detect when counter section is visible
            const counterSection = document.getElementById('impact');
            if (counterSection && 'IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            startCounterAnimations();
                            // Once counters are started, no need to observe anymore
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.2 }); // Trigger when 20% visible

                observer.observe(counterSection);
            }

            // Method 3: Fallback to scroll event if all else fails
            window.addEventListener('scroll', function scrollHandler() {
                const counterSection = document.getElementById('impact');
                if (counterSection) {
                    const rect = counterSection.getBoundingClientRect();
                    const windowHeight = window.innerHeight || document.documentElement.clientHeight;

                    if (rect.top <= windowHeight * 0.8 && rect.bottom >= windowHeight * 0.2) {
                        startCounterAnimations();
                        window.removeEventListener('scroll', scrollHandler);
                    }
                }
            });

            // Method 4: Last resort - trigger after a delay if user has scrolled
            let hasScrolled = false;
            window.addEventListener('scroll', function() {
                hasScrolled = true;
            }, { once: true });

            // If user has scrolled but counters haven't animated after 5 seconds, force animation
            setTimeout(() => {
                if (hasScrolled && !countersAnimated) {
                    startCounterAnimations();
                }
            }, 5000);
        });
    </script>

    <!-- Testimonial Slider Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.testimonial-slide');
            const dots = document.querySelectorAll('[data-index]');
            const prevButton = document.getElementById('prev-testimonial');
            const nextButton = document.getElementById('next-testimonial');
            let currentIndex = 0;
            let interval;
            const duration = 7000; // 7 seconds per slide
            let isTransitioning = false;

            // No progress bar or transition-width utility needed

            // Function to show a specific slide
            function showSlide(index) {
                if (isTransitioning) return;
                isTransitioning = true;

                // Hide current slide
                const currentSlide = document.querySelector('.testimonial-slide[data-active="true"]');
                if (currentSlide) {
                    // Hide text and author first
                    const currentText = currentSlide.querySelector('.testimonial-text');
                    const currentAuthor = currentSlide.querySelector('.testimonial-author-container');

                    currentText.classList.add('opacity-0', 'translate-y-4');
                    currentAuthor.classList.add('opacity-0', 'translate-y-4');

                    // Then fade out the slide after a short delay
                    setTimeout(() => {
                        currentSlide.classList.remove('opacity-100');
                        currentSlide.setAttribute('data-active', 'false');

                        // Show new slide after current slide fades out
                        setTimeout(() => {
                            // Show new slide
                            slides.forEach((slide, i) => {
                                if (i === index) {
                                    slide.classList.add('opacity-100');
                                    slide.setAttribute('data-active', 'true');

                                    // Animate in text and author
                                    const text = slide.querySelector('.testimonial-text');
                                    const author = slide.querySelector('.testimonial-author-container');

                                    setTimeout(() => {
                                        text.classList.remove('opacity-0', 'translate-y-4');

                                        setTimeout(() => {
                                            author.classList.remove('opacity-0', 'translate-y-4');
                                            isTransitioning = false;
                                        }, 500);
                                    }, 500);
                                }
                            });
                        }, 500);
                    }, 300);
                } else {
                    // First slide (no current active slide)
                    slides[index].classList.add('opacity-100');
                    slides[index].setAttribute('data-active', 'true');

                    // Animate in text and author
                    const text = slides[index].querySelector('.testimonial-text');
                    const author = slides[index].querySelector('.testimonial-author-container');

                    setTimeout(() => {
                        text.classList.remove('opacity-0', 'translate-y-4');

                        setTimeout(() => {
                            author.classList.remove('opacity-0', 'translate-y-4');
                            isTransitioning = false;
                        }, 500);
                    }, 500);
                }

                // Update dots
                dots.forEach((dot, i) => {
                    if (i === index) {
                        dot.classList.remove('bg-gray-300');
                        dot.classList.add('bg-primary');
                    } else {
                        dot.classList.remove('bg-primary');
                        dot.classList.add('bg-gray-300');
                    }
                });

                currentIndex = index;
            }

            // Start automatic slideshow
            function startSlideshow() {
                clearInterval(interval);
                interval = setInterval(() => {
                    if (!isTransitioning) {
                        const nextSlide = (currentIndex + 1) % slides.length;
                        showSlide(nextSlide);
                    }
                }, duration);
            }

            // Initialize slideshow
            showSlide(0);
            startSlideshow();

            // Event listeners for next and previous buttons
            if (nextButton) {
                nextButton.addEventListener('click', function() {
                    if (!isTransitioning) {
                        const nextSlide = (currentIndex + 1) % slides.length;
                        showSlide(nextSlide);
                        startSlideshow(); // Reset the interval
                    }
                });
            }

            if (prevButton) {
                prevButton.addEventListener('click', function() {
                    if (!isTransitioning) {
                        const prevSlide = (currentIndex - 1 + slides.length) % slides.length;
                        showSlide(prevSlide);
                        startSlideshow(); // Reset the interval
                    }
                });
            }

            // Event listeners for dots
            dots.forEach(dot => {
                dot.addEventListener('click', function() {
                    if (!isTransitioning) {
                        const index = parseInt(this.getAttribute('data-index'));
                        if (index !== currentIndex) {
                            showSlide(index);
                            startSlideshow(); // Reset the interval
                        }
                    }
                });
            });

            // No pause on hover functionality
        });
    </script>

    <!-- Enrollment Modal with Fade-in Animation -->
    <!-- <div id="enrollmentModal" class="fixed inset-0 z-[100] hidden opacity-0 transition-opacity duration-700">
        
        <div id="modalBackdrop" class="absolute inset-0 backdrop-blur-[2px]"></div>

        
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[95%] sm:w-[90%] md:w-[85%] max-w-2xl opacity-0 translate-y-8 transition-all duration-700 ease-out" id="modalContent">
            
            <div class="bg-dark border-2 border-primary overflow-hidden">
                
                <button id="closeModal" class="absolute top-2 right-2 sm:top-4 sm:right-4 text-white hover:text-primary z-20 bg-black/50 w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>

                
                <div class="bg-primary py-2 sm:py-3 px-4 sm:px-6 text-center relative">
                    <h2 class="text-black text-lg sm:text-xl font-bold uppercase tracking-wider">Enrollment Closed</h2>
                </div>

                
                <div class="p-4 sm:p-6 md:p-8">
                    
                    <h2 class="text-2xl sm:text-3xl font-bold text-white text-center mb-3 sm:mb-4">
                        <span class="text-primary">REGISTRATION FOR ARABIC BATCH-10 </span><br>
                        <span class="text-xl sm:text-2xl">IS NOW CLOSED</span>
                    </h2>

                    
                    <div class="mb-4 sm:mb-8">
                        <p class="text-white/90 text-sm sm:text-base text-center sm:text-justify mb-4 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-2 sm:px-4">
                            Experience a unique approach to understand Quranic Arabic that combines traditional and modern teaching methodologies. This carefully structured program breaks down complex grammatical concepts into accessible lessons, creating a clear path from basic understanding to advanced textual analysis. The Goal of this course to develop the skills to independently explore the linguistic treasures of the Quran and Hadith, opening new dimensions of meaning and guidance.
                        </p>
                    </div>

                    
                    <div class="flex justify-center mb-4 sm:mb-6">
                        <div class="grid grid-cols-5 gap-1 sm:gap-2 w-full max-w-md px-1 sm:px-2">
                            
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-clock text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">1 Hour</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">Duration</p>
                            </div>

                            
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-calendar-alt text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">Once</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">Per Week</p>
                            </div>

                            
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-gift text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">100% Free</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">No Cost</p>
                            </div>

                            
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-globe text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">English</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">Teaching</p>
                            </div>

                            
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-users text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">In Person</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">On-site only</p>
                            </div>
                        </div>
                    </div>

                    
                    <div class="text-center">
                        <a href="https://docs.google.com/forms/d/e/1FAIpQLSeJ_pwMI9MZgc4j91BQa_uDG7fgsIiKRH_OOJjsLwgkCTpmJQ/viewform?usp=header" target="_blank" id="modalEnrollButton" class="bg-primary hover:bg-primary/90 text-black font-bold text-sm sm:text-base px-6 sm:px-8 md:px-10 py-3 sm:py-4 inline-flex items-center transition-all duration-300">
                            <i class="fas fa-user-plus mr-2"></i>
                            Join the Waiting List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Enrollment Modal Script -->
    <script>
        // Initialize the enrollment modal functionality
        (function() {
            // Show modal with fade-in animation after 6 seconds
            setTimeout(function() {
                const modal = document.getElementById('enrollmentModal');
                const modalContent = document.getElementById('modalContent');

                if (modal) {
                    // First make it visible but still transparent
                    modal.classList.remove('hidden');

                    // Force a reflow to ensure the transition works
                    void modal.offsetWidth;

                    // Fade in the modal backdrop
                    modal.classList.add('opacity-100');

                    // Animate in the modal content
                    setTimeout(function() {
                        modalContent.classList.remove('opacity-0', 'translate-y-8');
                    }, 200);
                }
            }, 6000); // 6 seconds

            // Function to close modal without animation (just hide it immediately)
            function closeModalWithoutAnimation() {
                const modal = document.getElementById('enrollmentModal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            }

            // Add event listeners when DOM is fully loaded
            document.addEventListener('DOMContentLoaded', function() {
                // Close modal when clicking the close button
                const closeBtn = document.getElementById('closeModal');
                if (closeBtn) {
                    closeBtn.addEventListener('click', closeModalWithoutAnimation);
                }

                // Close modal when clicking the enroll button
                const enrollBtn = document.getElementById('modalEnrollButton');
                if (enrollBtn) {
                    enrollBtn.addEventListener('click', closeModalWithoutAnimation);
                }

                // Close modal when clicking outside the modal content
                const modal = document.getElementById('enrollmentModal');
                if (modal) {
                    modal.addEventListener('click', function(e) {
                        // Check if the click is on the modal backdrop (outside the modal content)
                        if (e.target === this || e.target.id === 'modalBackdrop') {
                            closeModalWithoutAnimation();
                        }
                    });
                }

                // Add specific listener for the backdrop
                const backdrop = document.getElementById('modalBackdrop');
                if (backdrop) {
                    backdrop.addEventListener('click', closeModalWithoutAnimation);
                }
            });
        })(); // Immediately invoke the function
    </script>

    <!-- Gallery Lightbox Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create lightbox elements
            const lightboxOverlay = document.createElement('div');
            lightboxOverlay.className = 'fixed inset-0 bg-black/90 z-50 hidden flex items-center justify-center';

            const lightboxContent = document.createElement('div');
            lightboxContent.className = 'relative max-w-4xl mx-auto p-4';

            const lightboxImage = document.createElement('img');
            lightboxImage.className = 'max-h-[80vh] max-w-full';

            const closeButton = document.createElement('button');
            closeButton.className = 'absolute top-0 right-0 -mt-12 -mr-12 bg-primary w-10 h-10 rounded-full flex items-center justify-center text-white';
            closeButton.innerHTML = '<i class="fas fa-times"></i>';

            // Append elements
            lightboxContent.appendChild(lightboxImage);
            lightboxContent.appendChild(closeButton);
            lightboxOverlay.appendChild(lightboxContent);
            document.body.appendChild(lightboxOverlay);

            // Add click event to gallery items
            const galleryLinks = document.querySelectorAll('.lightbox-link');
            galleryLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('href');
                    lightboxImage.src = imgSrc;
                    lightboxOverlay.classList.remove('hidden');
                    lightboxOverlay.classList.add('flex');
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                });
            });

            // Close lightbox when clicking close button
            closeButton.addEventListener('click', function() {
                lightboxOverlay.classList.add('hidden');
                lightboxOverlay.classList.remove('flex');
                document.body.style.overflow = ''; // Restore scrolling
            });

            // Close lightbox when clicking outside the image
            lightboxOverlay.addEventListener('click', function(e) {
                if (e.target === lightboxOverlay) {
                    lightboxOverlay.classList.add('hidden');
                    lightboxOverlay.classList.remove('flex');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });

            // Close lightbox with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !lightboxOverlay.classList.contains('hidden')) {
                    lightboxOverlay.classList.add('hidden');
                    lightboxOverlay.classList.remove('flex');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });
        });
    </script>



    <!-- Video preload and caching script -->
    <script>
        // Ensure video is preloaded and cached
        document.addEventListener('DOMContentLoaded', function() {
            const heroVideo = document.querySelector('video');
            if (heroVideo) {
                // Force the browser to cache the video
                heroVideo.load();

                // Add event listeners to monitor video loading
                heroVideo.addEventListener('loadeddata', function() {
                    console.log('Video data loaded successfully');
                });

                heroVideo.addEventListener('error', function(e) {
                    console.error('Error loading video:', e);
                });
            }
        });
    </script>

    <!-- AOS (Animate On Scroll) Initialization -->
    <script src="https://unpkg.com/aos@next/dist/aos.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: false,
                mirror: false,
                offset: 100
            });
        });
    </script>
</body>
</html>